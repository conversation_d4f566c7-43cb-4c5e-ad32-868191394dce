import React, {useEffect, useState, useRef} from 'react';
import {
  StyleSheet,
  View,
  Animated,
  FlatList,
  TouchableOpacity,
  Keyboard,
} from 'react-native';
import {Dropdown} from 'react-native-element-dropdown';
import {colors} from '../Custom/Colors';
import {hp, wp} from '../Custom/Responsiveness';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {OfficeServices} from '../api/services/officeServices';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AddOfficeDropDown = ({value, setValue, height = 4.9, istrue = false}) => {
  const [isFocus, setIsFocus] = useState(false);
  const [rotateAnimation] = useState(new Animated.Value(0));
  const [adminData, setAdminData] = useState([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [isLoading, setIsLoading] = useState(false);
  const [hasMore, setHasMore] = useState(true);
  const [initialLoaded, setInitialLoaded] = useState(false);
  const [shouldScrollToSelected, setShouldScrollToSelected] = useState(true); // New state to track scrolling
  const flatListRef = useRef(null); // Reference for FlatList
  const [user, setUser] = useState(null);
  const [keyboardVisible, setKeyboardVisible] = useState(false);
  const [dropdownReady, setDropdownReady] = useState(true);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          console.log('User data:', parsedUser);
          setUser(parsedUser);
          fetchData(1, '', parsedUser?.id);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);

  // Add keyboard listeners to track keyboard state
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener('keyboardDidShow', () => {
      setKeyboardVisible(true);
    });
    const keyboardDidHideListener = Keyboard.addListener('keyboardDidHide', () => {
      setKeyboardVisible(false);
      setDropdownReady(true);
    });

    return () => {
      keyboardDidShowListener?.remove();
      keyboardDidHideListener?.remove();
    };
  }, []);
  const rotateIcon = () => {
    // fetchData();
    Animated.timing(rotateAnimation, {
      toValue: isFocus ? 0 : 1,
      duration: 300,
      useNativeDriver: true,
    }).start();
  };

  const spin = rotateAnimation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  // useEffect(() => {
  //   fetchData(currentPage);
  // }, []);

  useEffect(() => {
    if (isFocus && value) {
      // Scroll to the selected item when dropdown is focused
      const index = adminData.findIndex(item => item.id === value.id);
      if (index !== -1 && flatListRef.current) {
        flatListRef.current.scrollToIndex({index, animated: true});
      }
    }
  }, [isFocus, value, adminData]);

  const fetchData = async (page = 1, search = '', adminId = user?.id) => {
    console.warn("userID---,",adminId);
    if (istrue) {
      if (isLoading || (!search && page <= currentPage && initialLoaded))
        return;
    }
    console.warn;
    setIsLoading(true);
    try {
      const response = await OfficeServices.getOffice({
        page,
        search,
        adminId,
      });
      setCurrentPage(page);
      setHasMore(response.data.totalPages > page);
      setInitialLoaded(true);
      setAdminData(prev =>
        page === 1 || search
          ? response.data.offices
          : [...prev, ...response.data.offices],
      );
    } catch (error) {
      console.error('Fetch error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderItem = item => {
    if (!item) return null;
    return (
      <View style={styles.itemContainer}>
        <View style={styles.itemTextContainer}>
          <ResponsiveText style={styles.itemName}>{item.name}</ResponsiveText>
        </View>
      </View>
    );
  };

  const handleChange = item => {
    console.log('Selected item:', item);
    setValue(item);
    setIsFocus(false);
    rotateIcon();
  };

  return (
    <View style={styles.container}>
      <Dropdown
        style={[
          styles.dropdown,
          {height: hp(height)},
          isFocus && styles.dropdownFocus,
          (!dropdownReady && keyboardVisible) && styles.dropdownDisabled,
        ]}
        disable={!dropdownReady && keyboardVisible}
        keyExtractor={(item, index) => item.id.toString() || index.toString()}
        onChangeText={text => fetchData(currentPage, text,user?.id)}
        selectedTextStyle={styles.selectedTextStyle}
        itemTextStyle={styles.dropdownItemText}
        containerStyle={styles.dropdownContainer}
        activeColor={colors.lightGrey6}
        data={dropdownReady ? adminData : []}
        maxHeight={hp(25)}
        labelField="name"
        valueField="id"
        placeholder={!dropdownReady && keyboardVisible ? "Loading..." : "Select Office"}
        placeholderStyle={styles.placeholderStyle}
        value={value ? value.id : ''}
        flatListProps={{
          ref: flatListRef,
          onEndReached: () => {
            if (hasMore && !isLoading) {
              fetchData(currentPage + 1 ,"",user?.id);
            }
          },
          onEndReachedThreshold: 0.5,
          getItemLayout: (data, index) => ({
            length: hp(5), // Adjust this based on your item height
            offset: hp(5) * index,
            index,
          }),
        }}
        dropdownPosition="bottom"
        onFocus={() => {
          if (keyboardVisible) {
            // If keyboard is visible, prevent dropdown from opening immediately
            setDropdownReady(false);
            setIsFocus(false); // Ensure dropdown doesn't show while keyboard is dismissing
            Keyboard.dismiss();
            // Wait for keyboard to fully dismiss before opening dropdown
            setTimeout(() => {
              setDropdownReady(true);
              setIsFocus(true);
              rotateIcon();
              setShouldScrollToSelected(true);
            }, 350); // Slightly longer delay for smoother transition
          } else {
            // If keyboard is not visible, open dropdown immediately
            setIsFocus(true);
            rotateIcon();
            setShouldScrollToSelected(true);
          }
        }}
        onBlur={() => {
          setIsFocus(false);
          rotateIcon();
          setShouldScrollToSelected(false); // Disable auto scroll when blurred
          setDropdownReady(true); // Reset dropdown ready state
        }}
        onChange={handleChange}
        renderItem={renderItem}
        renderRightIcon={() => (
          <Animated.View style={{flexDirection: 'row', alignItems: 'center'}}>
            <Icon source={globalpath.up} size={wp(3)} tintColor={colors.grey} />
          </Animated.View>
        )}
      />
    </View>
  );
};

export default AddOfficeDropDown;

const styles = StyleSheet.create({
  container: {
    width: '99%',
    position: 'relative', // Needed to position the dropdown correctly
    zIndex: 1, // Ensure proper layering
  },
  dropdown: {
    backgroundColor: colors.white,
    borderRadius: wp(2),
    paddingHorizontal: wp(4),
    borderWidth: 1,
    borderColor: colors.greyborder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 2,
  },
  dropdownFocus: {
    borderColor: colors.Light_theme_purple,
    borderWidth: 1.5,
    shadowOpacity: 0.1,
  },
  dropdownDisabled: {
    opacity: 0.6,
    backgroundColor: colors.lightGrey2,
  },
  dropdownContainer: {
    borderRadius: wp(2),
    borderWidth: 1,
    borderColor: colors.greyborder,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 3,
    // Let the library handle positioning with dropdownPosition prop
    backgroundColor: colors.white,
  },
  selectedTextStyle: {
    fontSize: wp(3.8),
    color: colors.black,
    fontWeight: '500',
  },
  placeholderStyle: {
    fontSize: wp(3.8),
    color: colors.darkGrey,
  },
  itemContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: wp(3),
    borderBottomWidth: 1,
    borderBottomColor: colors.lightGrey3,
  },
  itemTextContainer: {
    flex: 1,
  },
  itemName: {
    fontSize: wp(3.8),
    color: colors.black,
    fontWeight: '500',
  },
  itemEmail: {
    fontSize: wp(3.2),
    color: colors.darkGrey,
    marginTop: hp(0.5),
  },
  iconsContainer: {
    position: 'absolute',
    top: wp(2),
    right: wp(2),
    flexDirection: 'row',
    alignItems: 'center',
    zIndex: 1,
  },
  clearButton: {
    marginHorizontal: wp(4),
  },
});
