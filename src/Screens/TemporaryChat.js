import React, {useCallback, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {colors} from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import CustomHeader from '../Components/CustomHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socket from '../Sockets/socketService';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {hp, wp} from '../Custom/Responsiveness';
import {useFocusEffect} from '@react-navigation/native';
import LanguageModal from '../Components/LanguageModal';
const getLabel = code => {
  const item = {
    al: 'Albanian',
    bn: 'Bengali',
    ea: 'Egyption',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ma: 'Moroccan',
    sn: 'Shona',
    ta: 'Arabic',
    ur: 'Urdu',
    yo: 'Yoruba',
    zh: 'Chinese',
  };
  return item[code] || code;
};
const TemporaryChat = ({navigation}) => {
  const [user, setUser] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [language, setLanguage] = useState('it');

  const JoinChatRoom = (chat, idtype) => {
    const conversationId = `${idtype}_${chat.phoneNumber}`;
    socket.emit('joinChat', {chat, userDetails, conversationId, idtype});
  };


  useFocusEffect(
    useCallback(() => {
      const fetchUserData = async () => {
        try {
          const userData = await AsyncStorage.getItem('user');
          if (userData) {
            const parsedUser = JSON.parse(userData);
            console.log('parsed user', parsedUser);
            setUser(parsedUser);
            setUserDetails({
              active: null,
              avatar: null,
              email: null,
              id: parsedUser.user.id,
              name: parsedUser.user.firstName,
              role: 'client',
              phoneNumber: parsedUser.user.phoneNumber,
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };

      fetchUserData();
    }, []),
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <CustomHeader title="My Profile" />

      <ScrollView contentContainerStyle={styles.container}>
        {/* Minimalist Top Section */}
        <View style={styles.topSection}>
          <Image
            source={globalpath.logo}
            style={styles.compactLogo}
            resizeMode="contain"
          />
        </View>

        {/* Card-Based Layout */}
        {user && (
          <View style={styles.mainContent}>
            {/* User Identity Card */}
            <View style={styles.identityCard}>
              <View style={styles.avatarSection}>
                <View style={styles.hexagonAvatar}>
                  <ResponsiveText color={colors.white} size={6} weight={'700'}>
                    {user?.user?.firstName?.charAt(0)?.toUpperCase() || ''}
                  </ResponsiveText>
                </View>
                <View style={styles.userInfo}>
                  <ResponsiveText size={5.5} weight="bold" style={styles.userName}>
                    {user?.user?.firstName} {user?.user?.lastName}
                  </ResponsiveText>
                  <View style={styles.badgeContainer}>
                    <View style={styles.activeBadge}>
                      <ResponsiveText size={3} style={styles.badgeText}>
                        ACTIVE
                      </ResponsiveText>
                    </View>
                  </View>
                </View>
              </View>

              <TouchableOpacity
                style={styles.editAction}
                onPress={() => navigation.navigate('Profile', {user: user})}>
                <Icon
                  source={globalpath.edit}
                  size={wp(5)}
                  tintColor={colors.Light_theme_purple}
                />
              </TouchableOpacity>
            </View>

            {/* Information Grid */}
            <View style={styles.infoGrid}>
              {/* Phone Card */}
              <View style={styles.infoCard}>
                <View style={styles.cardHeader}>
                  <View style={styles.iconBadge}>
                    <Icon
                      source={globalpath.phonecall}
                      size={wp(4.5)}
                      tintColor={colors.Light_theme_purple}
                    />
                  </View>
                  <ResponsiveText size={3.5} style={styles.cardTitle}>
                    Contact
                  </ResponsiveText>
                </View>
                <ResponsiveText size={4.5} style={styles.cardValue}>
                  {user?.user?.phoneNumber}
                </ResponsiveText>
              </View>

              {/* Language Card */}
              <View style={styles.infoCard}>
                <View style={styles.cardHeader}>
                  <View style={styles.iconBadge}>
                    <Icon
                      source={globalpath.language}
                      size={wp(4.5)}
                      tintColor={colors.Light_theme_purple}
                    />
                  </View>
                  <ResponsiveText size={3.5} style={styles.cardTitle}>
                    Language
                  </ResponsiveText>
                </View>
                <TouchableOpacity
                  onPress={() => setShowModal(true)}
                  style={styles.languageChip}>
                  <ResponsiveText size={4} style={styles.chipText}>
                    {getLabel(language)}
                  </ResponsiveText>
                  <Icon
                    source={globalpath.down || globalpath.back}
                    size={wp(3)}
                    tintColor={colors.greyBlack}
                    style={styles.chipIcon}
                  />
                </TouchableOpacity>
              </View>
            </View>

            <LanguageModal
              visible={showModal}
              onClose={() => setShowModal(false)}
              onSelect={val => setLanguage(val)}
              selectedValue={language}
            />
          </View>
        )}
      </ScrollView>

      {/* Geometric Chat Button */}
      <TouchableOpacity
        style={styles.chatFab}
        onPress={() => {
          navigation.navigate('ChatScreen', {
            item: user.user,
            isFromCustomer: false,
            group: 'client',
            lang: language,
            tempChat: true,
          });
          JoinChatRoom(user.user, 'client');
        }}>
        <View style={styles.fabBackground} />
        <Icon source={globalpath.convo} size={wp(6.5)} tintColor={colors.white} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: colors.lightGrey2,
  },
  container: {
    paddingHorizontal: wp(4),
    paddingBottom: hp(12),
  },
  topSection: {
    alignItems: 'center',
    paddingVertical: hp(3),
    marginBottom: hp(2),
  },
  compactLogo: {
    width: wp(35),
    height: hp(8),
  },
  mainContent: {
    flex: 1,
  },
  identityCard: {
    backgroundColor: colors.white,
    borderRadius: wp(4),
    padding: wp(5),
    marginBottom: hp(3),
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.08,
    shadowRadius: 8,
    elevation: 4,
  },
  avatarSection: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  hexagonAvatar: {
    width: wp(16),
    height: wp(16),
    backgroundColor: colors.Light_theme_purple,
    borderRadius: wp(3),
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
    transform: [{rotate: '45deg'}],
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    color: colors.greyBlack,
    marginBottom: hp(0.5),
  },
  badgeContainer: {
    flexDirection: 'row',
  },
  activeBadge: {
    backgroundColor: colors.lightgreen,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.3),
    borderRadius: wp(1),
  },
  badgeText: {
    color: colors.white,
    fontWeight: '600',
  },
  editAction: {
    padding: wp(2),
  },
  infoGrid: {
    gap: hp(2),
  },
  infoCard: {
    backgroundColor: colors.white,
    borderRadius: wp(3),
    padding: wp(4),
    shadowColor: colors.black,
    shadowOffset: {width: 0, height: 1},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: hp(1.5),
  },
  iconBadge: {
    width: wp(8),
    height: wp(8),
    borderRadius: wp(2),
    backgroundColor: `${colors.Light_theme_purple}15`,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(3),
  },
  cardTitle: {
    color: colors.lightGrey5,
    fontWeight: '500',
  },
  cardValue: {
    color: colors.greyBlack,
    fontWeight: '600',
    marginLeft: wp(11),
  },
  languageChip: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey6,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.8),
    borderRadius: wp(5),
    marginLeft: wp(11),
    alignSelf: 'flex-start',
  },
  chipText: {
    color: colors.greyBlack,
    fontWeight: '500',
    marginRight: wp(1),
  },
  chipIcon: {
    transform: [{rotate: '90deg'}],
  },
  chatFab: {
    position: 'absolute',
    bottom: hp(4),
    right: wp(6),
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 6},
    shadowOpacity: 0.3,
    shadowRadius: 10,
    elevation: 8,
    overflow: 'hidden',
  },
  fabBackground: {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backgroundColor: colors.Light_theme_purple,
    borderRadius: wp(8),
  },
});

export default TemporaryChat;
