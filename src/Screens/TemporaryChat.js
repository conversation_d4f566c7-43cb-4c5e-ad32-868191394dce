import React, {useCallback, useState} from 'react';
import {
  View,
  StyleSheet,
  SafeAreaView,
  TouchableOpacity,
  Image,
  ScrollView,
} from 'react-native';
import {colors} from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import CustomHeader from '../Components/CustomHeader';
import AsyncStorage from '@react-native-async-storage/async-storage';
import socket from '../Sockets/socketService';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {hp, wp} from '../Custom/Responsiveness';
import {useFocusEffect} from '@react-navigation/native';
import LanguageModal from '../Components/LanguageModal';
const getLabel = code => {
  const item = {
    al: 'Albanian',
    bn: 'Bengali',
    ea: 'Egyption',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ma: 'Moroccan',
    sn: 'Shona',
    ta: 'Arabic',
    ur: 'Urdu',
    yo: 'Yoruba',
    zh: 'Chinese',
  };
  return item[code] || code;
};
const TemporaryChat = ({navigation}) => {
  const [user, setUser] = useState(null);
  const [userDetails, setUserDetails] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [language, setLanguage] = useState('it');

  const JoinChatRoom = (chat, idtype) => {
    const conversationId = `${idtype}_${chat.phoneNumber}`;
    socket.emit('joinChat', {chat, userDetails, conversationId, idtype});
  };


  useFocusEffect(
    useCallback(() => {
      const fetchUserData = async () => {
        try {
          const userData = await AsyncStorage.getItem('user');
          if (userData) {
            const parsedUser = JSON.parse(userData);
            console.log('parsed user', parsedUser);
            setUser(parsedUser);
            setUserDetails({
              active: null,
              avatar: null,
              email: null,
              id: parsedUser.user.id,
              name: parsedUser.user.firstName,
              role: 'client',
              phoneNumber: parsedUser.user.phoneNumber,
            });
          }
        } catch (error) {
          console.error('Error fetching user data:', error);
        }
      };

      fetchUserData();
    }, []),
  );

  return (
    <SafeAreaView style={styles.safeArea}>
      <CustomHeader title="My Profile" />

      <ScrollView contentContainerStyle={styles.container}>
        {/* Modern Header with Gradient Effect */}
        <View style={styles.header}>
          <View style={styles.headerGradient}>
            <Image
              source={globalpath.logo}
              style={styles.logo}
              resizeMode="contain"
            />

            {user && (
              <View style={styles.avatarContainer}>
                <View style={styles.avatarGlow}>
                  <View style={styles.avatar}>
                    <ResponsiveText color={colors.white} size={5} weight={'600'}>
                      {user?.user?.firstName?.charAt(0)?.toUpperCase() || ''}
                    </ResponsiveText>
                  </View>
                </View>
              </View>
            )}
          </View>
        </View>

        {/* Modern Glassmorphism Profile Card */}
        {user && (
          <View style={styles.profileCard}>
            <View style={styles.cardOverlay}>
              <View style={styles.profileHeader}>
                <View style={styles.nameSection}>
                  <ResponsiveText size={5} weight="bold" style={styles.nameText}>
                    {user?.user?.firstName} {user?.user?.lastName}
                  </ResponsiveText>
                  <View style={styles.statusIndicator}>
                    <View style={styles.onlineStatus} />
                    <ResponsiveText size={3} style={styles.statusText}>
                      Online
                    </ResponsiveText>
                  </View>
                </View>
                <TouchableOpacity
                  style={styles.editButton}
                  onPress={() => navigation.navigate('Profile', {user: user})}>
                  <View style={styles.editIconContainer}>
                    <Icon
                      source={globalpath.edit}
                      size={wp(4.5)}
                      tintColor={colors.white}
                    />
                  </View>
                </TouchableOpacity>
              </View>

              <View style={styles.detailsSection}>
                <View style={styles.detailRow}>
                  <View style={styles.modernIconContainer}>
                    <Icon
                      source={globalpath.phonecall}
                      size={wp(5)}
                      tintColor={colors.white}
                    />
                  </View>
                  <View style={styles.detailContent}>
                    <ResponsiveText size={3.5} style={styles.detailLabel}>
                      Phone Number
                    </ResponsiveText>
                    <ResponsiveText size={4} style={styles.detailText}>
                      {user?.user?.phoneNumber}
                    </ResponsiveText>
                  </View>
                </View>

                <View style={styles.detailRow}>
                  <View style={styles.modernIconContainer}>
                    <Icon
                      source={globalpath.language}
                      size={wp(5)}
                      tintColor={colors.white}
                    />
                  </View>
                  <View style={styles.detailContent}>
                    <ResponsiveText size={3.5} style={styles.detailLabel}>
                      Language
                    </ResponsiveText>
                    <TouchableOpacity
                      onPress={() => setShowModal(true)}
                      style={styles.languageSelector}>
                      <ResponsiveText size={4} style={styles.languageText}>
                        {getLabel(language)}
                      </ResponsiveText>
                      <Icon
                        source={globalpath.down || globalpath.back}
                        size={wp(3.5)}
                        tintColor={colors.Light_theme_purple}
                        style={styles.dropdownIcon}
                      />
                    </TouchableOpacity>
                  </View>
                </View>

                <LanguageModal
                  visible={showModal}
                  onClose={() => setShowModal(false)}
                  onSelect={val => setLanguage(val)}
                  selectedValue={language}
                />
              </View>
            </View>
          </View>
        )}
      </ScrollView>

      {/* Modern Floating Chat Button */}
      <TouchableOpacity
        style={styles.chatButton}
        onPress={() => {
          navigation.navigate('ChatScreen', {
            item: user.user,
            isFromCustomer: false,
            group: 'client',
            lang: language,
            tempChat: true,
          });
          JoinChatRoom(user.user, 'client');
        }}>
        <View style={styles.chatButtonInner}>
          <Icon source={globalpath.convo} size={wp(6)} tintColor={colors.white} />
        </View>
        <View style={styles.pulseRing} />
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#F8FAFC',
  },
  container: {
    paddingBottom: hp(12),
    alignItems: 'center',
  },
  header: {
    width: '100%',
    alignItems: 'center',
    paddingTop: hp(2),
    paddingBottom: hp(4),
    position: 'relative',
  },
  headerGradient: {
    width: '100%',
    alignItems: 'center',
    backgroundColor: colors.white,
    borderBottomLeftRadius: wp(10),
    borderBottomRightRadius: wp(10),
    paddingTop: hp(3),
    paddingBottom: hp(4),
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.08,
    shadowRadius: 16,
    elevation: 8,
  },
  logo: {
    width: wp(45),
    height: hp(12),
    marginBottom: hp(2),
  },
  avatarContainer: {
    position: 'absolute',
    bottom: -hp(6),
    zIndex: 10,
  },
  avatarGlow: {
    width: wp(26),
    height: wp(26),
    borderRadius: wp(13),
    backgroundColor: `${colors.Light_theme_purple}20`,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.3,
    shadowRadius: 16,
    elevation: 12,
  },
  avatar: {
    width: wp(22),
    height: wp(22),
    borderRadius: wp(11),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: 3,
    borderColor: colors.white,
  },
  profileCard: {
    width: wp(90),
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: wp(6),
    marginTop: hp(9),
    overflow: 'hidden',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 12},
    shadowOpacity: 0.1,
    shadowRadius: 20,
    elevation: 10,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
  },
  cardOverlay: {
    padding: wp(6),
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
  },
  profileHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: hp(3),
  },
  nameSection: {
    flex: 1,
  },
  nameText: {
    color: colors.greyBlack,
    fontSize: wp(5.5),
    marginBottom: hp(0.5),
  },
  statusIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: hp(0.5),
  },
  onlineStatus: {
    width: wp(2.5),
    height: wp(2.5),
    borderRadius: wp(1.25),
    backgroundColor: colors.lightgreen,
    marginRight: wp(2),
  },
  statusText: {
    color: colors.lightgreen,
    fontSize: wp(3.5),
  },
  editButton: {
    padding: wp(1),
  },
  editIconContainer: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 5,
  },
  detailsSection: {
    marginTop: hp(1),
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: hp(2),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.03)',
  },
  modernIconContainer: {
    width: wp(12),
    height: wp(12),
    borderRadius: wp(6),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: wp(4),
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 4},
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 4,
  },
  detailContent: {
    flex: 1,
    justifyContent: 'center',
  },
  detailLabel: {
    color: colors.lightGrey5,
    fontSize: wp(3.5),
    marginBottom: hp(0.3),
  },
  detailText: {
    color: colors.greyBlack,
    fontSize: wp(4.2),
    fontWeight: '500',
  },
  languageSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: colors.white,
    borderWidth: 1.5,
    borderColor: colors.lightGrey3,
    paddingVertical: hp(1.5),
    paddingHorizontal: wp(4),
    borderRadius: wp(3),
    marginTop: hp(0.5),
    shadowColor: '#000',
    shadowOffset: {width: 0, height: 2},
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  languageText: {
    color: colors.greyBlack,
    fontSize: wp(4),
    fontWeight: '500',
    flex: 1,
  },
  dropdownIcon: {
    transform: [{rotate: '90deg'}],
    marginLeft: wp(2),
  },
  chatButton: {
    position: 'absolute',
    bottom: hp(4),
    right: wp(6),
    width: wp(18),
    height: wp(18),
    borderRadius: wp(9),
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: colors.Light_theme_purple,
    shadowOffset: {width: 0, height: 8},
    shadowOpacity: 0.4,
    shadowRadius: 12,
    elevation: 12,
  },
  chatButtonInner: {
    width: wp(16),
    height: wp(16),
    borderRadius: wp(8),
    backgroundColor: colors.Light_theme_purple,
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 2,
  },
  pulseRing: {
    position: 'absolute',
    width: wp(18),
    height: wp(18),
    borderRadius: wp(9),
    backgroundColor: `${colors.Light_theme_purple}30`,
    zIndex: 1,
  },
});

export default TemporaryChat;
