import React, {useState, useRef, useEffect, useCallback} from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  SafeAreaView,
  Keyboard,
  Text,
  ActivityIndicator,
  Alert,
  Animated,
} from 'react-native';
import {wp, hp} from '../Custom/Responsiveness';
import useTheme from '../Redux/useTheme';
import {colors} from '../Custom/Colors';
import ResponsiveText from '../Custom/RnText';
import Icon from '../Custom/Icon';
import {globalpath} from '../Custom/globalpath';
import {Buffer} from 'buffer';
import MessageBubble from '../Components/MessageBubble';
import socket from '../Sockets/socketService';
import AsyncStorage from '@react-native-async-storage/async-storage';
import ChatAttachmentButton from '../Components/ChatAttachmentButton';
import RNFS from 'react-native-fs';
import LanguageDropDown from '../Components/LanguageDropDown';
import ImageResizer from 'react-native-image-resizer';
import {useFocusEffect} from '@react-navigation/native';
import LanguageModal from '../Components/LanguageModal';
import {OperatorServices} from '../api/services/operatorServices';
import * as Animatable from 'react-native-animatable';

const MAX_HEIGHT = hp(15);
const getLabel = code => {
  const item = {
    al: 'Albanian',
    bn: 'Bengali',
    ea: 'Egyption',
    en: 'English',
    es: 'Spanish',
    fr: 'French',
    hi: 'Hindi',
    it: 'Italian',
    ma: 'Moroccan',
    sn: 'Shona',
    ta: 'Arabic',
    ur: 'Urdu',
    yo: 'Yoruba',
    zh: 'Chinese',
  };
  return item[code] || code;
};
const ChatScreen = ({route, navigation}) => {
  const {item, isFromCustomer, group, lawyer, tempChat, lang} = route.params;
  console.warn('iteeem', item);
  const {getTextColor, backgroundColor, themeColor, getSecondaryTextColor} =
    useTheme();
  const [messages, setMessages] = useState([]);
  const [inputText, setInputText] = useState('');
  const [inputHeight, setInputHeight] = useState(hp(6));
  const [chatType, setChatType] = useState(group);
  const [showStartChat, setShowStartChat] = useState(false);
  const [userDetails, setUserDetails] = useState(null);
  const [selectedFile, setSelectedFile] = useState(null);
  const [chatLoading, setChatLoading] = useState(true);
  const [language, setLanguage] = useState(lang || 'it');
  const [onlineStatus, setOnlineStatus] = useState([]);
  const scrollViewRef = useRef(null);
  const [showModal, setShowModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [onlineStatusUser, setOnlineStatusUser] = useState([]);
  console.log('language===', language);
  useEffect(() => {
    setOnlineStatusUser(onlineStatus);
  }, [onlineStatus]);
  useEffect(() => {
    if (lang) {
      setLanguage(lang);
    }
  }, [lang]);
  const fetchAllowedAdmins = async () => {
    try {
      const payload = {
        adminId: userDetails.id,
        clientConversationId: `${'client'}_${item.phoneNumber}`,
        internalConversationId: `${'internal'}_${item.phoneNumber}`,
        lawyerConversationId: `${'lawyer'}_${item.phoneNumber}`,
        userDetails: userDetails,
        immigrantId: item.id,
      };

      const response = await OperatorServices.checkConversations(payload);
    } catch (err) {
      console.log('API Error:', err);
    }
  };
  const fetchUserData = async () => {
    try {
      const userData = await AsyncStorage.getItem('user');
      if (userData) {
        const parsedUser = JSON.parse(userData);
        console.warn('parsedUser in user data', parsedUser);
        // Destructure only needed properties
        const {active, avatar, email, id, name, role} = parsedUser;

        // Set state with only the required fields
        setUserDetails({
          active,
          avatar,
          email,
          id,
          name,
          role,
          phoneNumber: null,
        });
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };
  const fetchCustomerData = async () => {
    try {
      const userData = await AsyncStorage.getItem('user');
      if (userData) {
        const parsedUser = JSON.parse(userData);
        console.warn('parsedUser in customer data', parsedUser);

        // Destructure only needed properties
        const {active, avatar, email, id, name, role} = parsedUser;

        // Set state with only the required fields
        setUserDetails({
          active: null,
          avatar: null,
          email: null,
          id: parsedUser.user.id,
          name: parsedUser.user.firstName + ' ' + parsedUser.user.lastName,
          role: 'client',
          phoneNumber: parsedUser.user.phoneNumber,
        });
      }
    } catch (error) {
      console.error('Error fetching user data:', error);
    }
  };
  useEffect(() => {
    if (isFromCustomer) {
      fetchUserData();
    } else {
      fetchCustomerData();
    }
  }, []);
  const handleMessage = newMessage => {
    console.log('Got message:', newMessage);
    setMessages(prev => {
      const isDuplicate = prev.some(msg => msg.id === newMessage.id);
      if (!isDuplicate) {
        setLoading(false);
        markAsRead(); // Call only when message is added
        return [...prev, newMessage];
      }
      return prev;
    });
  };
  const JoinChatRoom = (chat, idtype) => {
    const conversationId = `${idtype}_${chat.phoneNumber}`;
    console.log('conversationId', conversationId);
    console.log('immigrant==', chat);
    console.log('admin==', userDetails);
    console.log('idType==-----', idtype);

    socket.emit('joinChat', {chat, userDetails, conversationId, idtype});
  };
  const leaveRoom = () => {
    socket.emit('leaveRoom', {
      userDetails,
    });
  };
  const handleChatJoined = data => {
    console.log('chatJoined response:', data);

    if (data.success === false && data.message === 'Unauthorized') {
      Alert.alert('Unauthorized', 'You are not allowed to join this chat.', [
        {
          text: 'Go Back',
          onPress: async () => {
            await leaveRoom();
            setLoading(false);
            navigation.goBack();
          },
          style: 'cancel',
        },
      ]);
    }
  };
  const updateOnlineStatus = statusArray => {
    console.log('statusarray', statusArray);
    setOnlineStatus(prev => {
      if (!prev) {
        return {
          [statusArray.roomId]: statusArray.status,
        };
      }
      return {
        ...prev,
        [statusArray.roomId]: statusArray.status,
      };
    });
  };

  useFocusEffect(
    useCallback(() => {
      if (!userDetails) return;

      setMessages([]);
      setChatLoading(true);

      socket.auth = {
        username: userDetails?.name,
        email: userDetails?.email || null,
        role: userDetails?.role,
        phoneNumber: userDetails?.phoneNumber || null,
        userId: userDetails?.id,
      };

      const handleConnect = () => {
        console.log('Socket connected:', socket.connected);
      };

      const handleConversationMessages = (prevAllMessages, idtypeNam) => {
        console.log('prevAllMessages', prevAllMessages);
        setMessages(prevAllMessages?.prevAllMessages || []);
        setChatLoading(false);
        markAsRead();
      };

      const handleMessageDeleted = payload => {
        console.log('🧹 Message Deleted from server:', payload);
        const deletedId = payload.messageId;

        setMessages(prev =>
          prev.map(msg =>
            msg.id === payload.messageId ? payload.message : msg,
          ),
        );
      };

      socket.on('connect', handleConnect);
      socket.on('newMessage', handleMessage);
      socket.on('chatJoined', handleChatJoined);
      socket.on('conversationMessages', handleConversationMessages);
      socket.on('onlineStatus', statusData => {
        console.log('Online Status Update:', statusData);
        updateOnlineStatus(statusData);
      });
      socket.on('messageDeleted', handleMessageDeleted); // ✅ NEW listener

      if (!socket.connected) {
        socket.connect();
      }

      setTimeout(() => {
        console.log('Joining room:', item, group);
        JoinChatRoom(item, group);
      }, 200);

      return () => {
        console.log('Leaving room:', item, group);
        socket.emit('leaveRoom', {userDetails});

        socket.off('connect', handleConnect);
        socket.off('newMessage', handleMessage);
        socket.off('conversationMessages', handleConversationMessages);
        socket.off('onlineStatus');
        socket.off('chatJoined', handleChatJoined);
        socket.off('messageDeleted', handleMessageDeleted); // ✅ CLEANUP
      };
    }, [userDetails, item, group, lawyer]),
  );

  const groupMessagesByDate = messages => {
    const groupedMessages = {};
    const currentDate = new Date();
    const oneDay = 24 * 60 * 60 * 1000;

    messages.forEach(message => {
      let date;
      const messageDate = new Date(message.createdAt);

      if (messageDate.toDateString() === currentDate.toDateString()) {
        date = 'Today';
      } else if (
        messageDate.toDateString() ===
        new Date(currentDate.getTime() - oneDay).toDateString()
      ) {
        date = 'Yesterday';
      } else {
        const day = String(messageDate.getDate()).padStart(2, '0');
        const month = String(messageDate.getMonth() + 1).padStart(2, '0');
        const year = messageDate.getFullYear();
        date = `${day}/${month}/${year}`;
      }

      if (!groupedMessages[date]) {
        groupedMessages[date] = [];
      }
      groupedMessages[date].push(message);
    });

    return groupedMessages;
  };

  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => {
        scrollViewRef.current?.scrollToEnd({animated: true});
      },
    );
    return () => {
      keyboardDidShowListener.remove();
    };
  }, []);

  const handleFileSelected = async file => {
    console.log('file', file);
    try {
      const fileExtension = file?.name?.split('.').pop()?.toLowerCase();
      const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'];

      let decodedUri = decodeURIComponent(file.uri);
      let filePath = decodedUri.replace('file://', '');

      // Check if the file is an image
      if (imageExtensions.includes(fileExtension)) {
        const resizedImage = await ImageResizer.createResizedImage(
          file.uri,
          800,
          800,
          'JPEG',
          90,
        );
        filePath = resizedImage.uri.replace('file://', '');
      } else {
        // Handle non-image files directly (e.g., PDF)
        filePath = decodedUri.replace('file://', '');
      }

      // Read file as base64 (resized if image, original otherwise)
      const base64Data = await RNFS.readFile(filePath, 'base64');

      const updatedFile = {
        name: file.name,
        type: file.type,
        data: base64Data,
      };

      setSelectedFile(updatedFile);
    } catch (err) {
      console.error('Error resizing or reading file:', err);
    }
  };

  const sendMessage = async () => {
    try {
      if (inputText.trim() || selectedFile) {
        const userData = await AsyncStorage.getItem('user');

        const parsedUser = JSON.parse(userData);

        const messageData = {
          chatId: item.phoneNumber,
          userDetails,
          sender: userDetails?.email || userDetails?.phoneNumber,
          groupName: group,
          content: inputText,
          senderType: isFromCustomer ? 'admin' : 'client',
          fileData: selectedFile?.data || null,
          fileName: selectedFile?.name || null,
          messageType: selectedFile ? 'file' : 'text',
          adminIdUser: isFromCustomer
            ? userDetails?.id
            : parsedUser?.user?.adminId,
        };
        setLoading(true);
        // if (selectedFile) {
        //   setLoading(true); // ✅ Set loading true if it's a file
        // }

        socket.emit('sendMessage', messageData); // Send message object
        setInputText('');
        setSelectedFile(null);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleDeleteMessage = message => {
    const messageId = message.id;

    const userData = {
      active: userDetails?.active,
      avatar: userDetails?.avatar,
      email: userDetails?.email,
      id: userDetails?.id,
      name: userDetails?.name,
      role: userDetails?.role,
    };

    socket.emit('deleteMessage', {messageId, userDetails: userData});
    // setMessages(prev => prev.filter(msg => msg.id !== messageId));

    // Add any actual deletion logic here, for example:
    // - Remove from local messages list
    // - Emit socket event
    // - Call backend API
  };

  const markAsRead = () => {
    const conversationId = `${group}_${item.phoneNumber}`;

    socket.emit('markAsRead', {conversationId});
  };

  const [role, setRole] = useState('');
  const [user, setUser] = useState(null);
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userData = await AsyncStorage.getItem('user');
        if (userData) {
          const parsedUser = JSON.parse(userData);
          setUser(parsedUser);

          const practiceNumber = parsedUser.user?.practiceNumber;

          setRole(practiceNumber);
        }
      } catch (error) {
        console.error('Error fetching user data:', error);
      }
    };

    fetchUserData();
  }, []);
  const pulseAnim = useRef(new Animated.Value(1)).current;

  useEffect(() => {
    if (
      onlineStatusUser &&
      onlineStatusUser[`${group}_${item.phoneNumber}`] === 'online'
    ) {
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800, // Faster duration
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 0,
            duration: 800, // Faster duration
            useNativeDriver: true,
          }),
        ]),
      ).start();
    } else {
      pulseAnim.setValue(0); // Reset animation when offline
    }
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <KeyboardAvoidingView
        style={[styles.container, {backgroundColor}]}
        behavior={Platform.OS === 'ios' ? 'padding' : undefined}>
        <View style={[styles.header, {backgroundColor}]}>
          <TouchableOpacity
            onPress={async () => {
              setLoading(false);
              await leaveRoom();
              navigation.goBack();
            }}>
            <Icon
              source={globalpath.back}
              size={wp(7)}
              tintColor={getTextColor()}
              margin={[0, 0, 0, wp(2)]}
            />
          </TouchableOpacity>
          <View style={styles.headerContent}>
            {/* <View style={[styles.avatar, {backgroundColor: themeColor}]}>
              <ResponsiveText color={colors.white} size={5.4} weight={'600'}>
                {item?.name?.charAt(0)?.toUpperCase() ||
                  item?.firstName?.charAt(0)?.toUpperCase() ||
                  ''}
              </ResponsiveText>
            </View> */}
            <View style={[styles.avatarContainer]}>
              <View style={[styles.avatar, {backgroundColor: themeColor}]}>
                <ResponsiveText color={colors.white} size={5.4} weight={'600'}>
                  {item?.name?.charAt(0)?.toUpperCase() ||
                    item?.firstName?.charAt(0)?.toUpperCase() ||
                    ''}
                </ResponsiveText>
              </View>

              {/* Online status indicator with pulse animation */}
              {onlineStatusUser &&
              onlineStatusUser[`${group}_${item.phoneNumber}`] === 'online' ? (
                <View style={styles.onlineIndicator}>
                  <Animated.View
                    style={[
                      styles.pulseDot,
                      {
                        transform: [
                          {
                            scale: pulseAnim.interpolate({
                              inputRange: [0, 1],
                              outputRange: [1, 1.5], // More noticeable scale
                            }),
                          },
                        ],
                        opacity: pulseAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0.8, 0], // More dramatic fade
                        }),
                      },
                    ]}
                  />
                  <View style={styles.solidDot} />
                </View>
              ) : undefined}
            </View>
            <View style={styles.headerTextContainer}>
              <View style={styles.nameContainer}>
                {/* {group === 'lawyer' ? (
                  <ResponsiveText
                    color={getTextColor()}
                    size={4}
                    weight={'600'}
                    numberOfLines={1}>
                    {`Lawyer's Chat - ${item?.practiceNumber}`}
                  </ResponsiveText>
                ) : ( */}
                  <ResponsiveText
                    color={getTextColor()}
                    size={4}
                    weight={'600'}
                    numberOfLines={1}
                    maxWidth={wp(50)}
                    >
                    {/* {item?.name || item.firstName} */}
                    immigrato
                    
                  </ResponsiveText>
                {/* )} */}

                {/* {onlineStatusUser &&
                onlineStatusUser[`${group}_${item.phoneNumber}`] ===
                  'online' ? (
                  <ResponsiveText
                    color={getTextColor()}
                    size={3}
                    style={{opacity: 0.7}}>
                    {onlineStatusUser[`${group}_${item.phoneNumber}`]}
                  </ResponsiveText>
                ) : ( */}
                  <ResponsiveText
                    color={getTextColor()}
                    size={3}
                    style={{opacity: 0.7}}
                    numberOfLines={1}
                    maxWidth={wp(50)}
                    >
                    Adeel Nazeer,Hassan farooq,Haris Khan
                  </ResponsiveText>
                {/* // )} */}
              </View>
              {tempChat ? (
                <ResponsiveText weight={'600'} margin={[0, wp(4), 0, wp(0)]}>
                  {role}
                </ResponsiveText>
              ) : (
                <View>
                  <TouchableOpacity onPress={() => setShowModal(true)}>
                    <Icon
                      source={globalpath.dots}
                      margin={[0, wp(2.5), 0, wp(0)]}
                      tintColor={getTextColor()}
                    />
                  </TouchableOpacity>

                  {/* <TouchableOpacity
                    onPress={() => setShowModal(true)}
                    style={{
                      backgroundColor: colors.white,
                      borderWidth: 1,
                      borderColor: colors.greyborder,
                      paddingVertical: wp(3),
                      paddingHorizontal: wp(4),
                      borderRadius: wp(2),
                      width: wp(25),
                    }}>
                    <ResponsiveText>{getLabel(language)}</ResponsiveText>
                  </TouchableOpacity> */}

                  <LanguageModal
                    visible={showModal}
                    onClose={() => setShowModal(false)}
                    onSelect={val => setLanguage(val)}
                    selectedValue={language}
                  />
                </View>
              )}

              {/* <View
                style={{
                  backgroundColor: colors.Light_theme_purple,
                  paddingHorizontal: wp(4),
                  paddingVertical: hp(1),
                  borderRadius: wp(5),
                }}>
                <ResponsiveText color={colors.white} size={4} weight={'500'}>
                  {chatType}
                </ResponsiveText>
              </View> */}
            </View>
          </View>
        </View>

        {chatLoading ? (
          <View
            style={{
              flex: 1,
              justifyContent: 'center',
              alignItems: 'center',
            }}>
            <ActivityIndicator size="large" color={themeColor} />
          </View>
        ) : (
          <View style={styles.messageListContainer}>
            {messages.length === 0 &&
            (chatType === 'internal' || chatType === 'lawyer') ? (
              <View style={styles.emptyStateContainer}>
                <Icon
                  source={
                    chatType === 'internal' ? globalpath.convo : globalpath.user
                  }
                  size={wp(15)}
                  tintColor={`${themeColor}50`}
                />
                <ResponsiveText
                  color={getTextColor()}
                  size={4}
                  weight={'500'}
                  style={styles.emptyStateText}>
                  {chatType === 'internal'
                    ? 'Start an internal conversation'
                    : 'Begin legal consultation'}
                </ResponsiveText>
              </View>
            ) : (
              <ScrollView
                ref={scrollViewRef}
                contentContainerStyle={styles.scrollViewContent}
                onContentSizeChange={() =>
                  scrollViewRef.current?.scrollToEnd({animated: true})
                }>
                {Object.entries(groupMessagesByDate(messages)).map(
                  ([date, messagesForDate]) => (
                    <View key={date}>
                      <View style={styles.dateHeaderContainer}>
                        <View style={styles.dateHeader}>
                          <Text style={styles.dateHeaderText}>{date}</Text>
                        </View>
                      </View>
                      {messagesForDate.map(message => (
                        <MessageBubble
                          key={message.id}
                          message={message}
                          userDetails={userDetails}
                          language={language}
                          onLongPress={() => {
                            Alert.alert(
                              'Delete Message',
                              'Are you sure you want to delete this message?',
                              [
                                {
                                  text: 'Cancel',
                                  style: 'cancel',
                                },
                                {
                                  text: 'Yes',
                                  onPress: () => handleDeleteMessage(message),
                                },
                              ],
                              {cancelable: true},
                            );
                          }}
                        />
                      ))}
                    </View>
                  ),
                )}
              </ScrollView>
            )}
            {selectedFile && (
              <View style={styles.selectedFileContainer}>
                <ResponsiveText size={3} numberOfLines={1}>
                  {selectedFile.name}
                </ResponsiveText>
                <TouchableOpacity
                  onPress={() => setSelectedFile(null)}
                  style={styles.removeFileButton}>
                  <Icon
                    source={globalpath.del}
                    size={wp(4)}
                    tintColor={colors.red}
                  />
                </TouchableOpacity>
              </View>
            )}
            {/* Input */}
            {/* {!showStartChat && (
              <View
                style={[
                  styles.inputContainer,
                  {borderTopColor: getSecondaryTextColor()},
                ]}>
                <TextInput
                  style={[styles.textInput, {height: inputHeight}]}
                  placeholder="Write a message"
                  value={inputText}
                  onChangeText={setInputText}
                  placeholderTextColor={colors.grey}
                  multiline={true}
                  onContentSizeChange={event => {
                    const newHeight = event.nativeEvent.contentSize.height;
                    setInputHeight(
                      Math.min(MAX_HEIGHT, Math.max(hp(6), newHeight)),
                    );
                  }}
                />

                {group !== 'internal' && (
                  <ChatAttachmentButton onFileSelected={handleFileSelected} />
                )}

                <TouchableOpacity
                  style={[styles.sendButton,{backgroundColor:themeColor}]}
                  onPress={sendMessage}
                  disabled={!inputText.trim() && !selectedFile}>
                  <Icon
                    source={globalpath.sendmessages}
                    resizeMode="contain"
                    size={wp(5)}
                    tintColor={
                      inputText.trim() || selectedFile ? colors.white : colors.white
                    }
                  />
                </TouchableOpacity>
              </View>
            )} */}
            {!(
              userDetails?.role === 'firstContact' &&
              group === 'lawyer'
            ) &&
              !showStartChat && (
                <View
                  style={[
                    styles.inputContainer,
                    {borderTopColor: getSecondaryTextColor()},
                  ]}>
                  {/* TextInput + Attachment + Conditional Send Button */}
                  <View style={styles.inputWrapper}>
                    <TextInput
                      style={[styles.textInput, {height: inputHeight}]}
                      placeholder="Write a message"
                      value={inputText}
                      onChangeText={setInputText}
                      placeholderTextColor={colors.grey}
                      multiline={true}
                      onContentSizeChange={event => {
                        const newHeight = event.nativeEvent.contentSize.height;
                        setInputHeight(
                          Math.min(MAX_HEIGHT, Math.max(hp(6), newHeight)),
                        );
                      }}
                    />

                    <ChatAttachmentButton
                      onFileSelected={handleFileSelected}
                      style={styles.attachmentButton}
                    />

                    {(inputText.trim() || selectedFile || loading) && (
                      <Animatable.View
                        animation="bounceIn"
                        duration={300}
                        style={[
                          styles.sendButton,
                          {backgroundColor: themeColor},
                        ]}>
                        <TouchableOpacity
                          onPress={sendMessage}
                          disabled={!inputText.trim() && !selectedFile}>
                          {loading ? (
                            <ActivityIndicator
                              size="small"
                              color={colors.white}
                            />
                          ) : (
                            <Icon
                              source={globalpath.sendmessages}
                              resizeMode="contain"
                              size={wp(4.5)}
                              tintColor={colors.white}
                            />
                          )}
                        </TouchableOpacity>
                      </Animatable.View>
                    )}
                  </View>
                </View>
              )}
          </View>
        )}
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: wp(1),
    paddingVertical: hp(1),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0, 0, 0, 0.1)',
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    marginLeft: wp(2),
  },
  // avatar: {
  //   width: wp(10),
  //   height: wp(10),
  //   borderRadius: wp(5),
  //   justifyContent: 'center',
  //   alignItems: 'center',
  // },
  avatarContainer: {
    position: 'relative',
    width: wp(10),
    height: wp(10),
  },
  avatar: {
    width: wp(10),
    height: wp(10),
    borderRadius: wp(5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  onlineIndicator: {
    position: 'absolute',
    right: wp(-0.5),
    bottom: wp(-0.5),
    width: wp(3.5),
    height: wp(3.5),
    justifyContent: 'center',
    alignItems: 'center',
  },
  solidDot: {
    width: wp(2.7),
    height: wp(2.7),
    borderRadius: wp(1.25),
    backgroundColor: '#4CAF50',
    borderWidth: 2,
    borderColor: 'white',
    position: 'absolute',
  },
  pulseDot: {
    width: wp(3.5),
    height: wp(3.5),
    borderRadius: wp(1.75),
    backgroundColor: '#4CAF50',
    position: 'absolute',
  },
  pulse: {
    width: '100%',
    height: '100%',
    borderRadius: wp(1.5),
    backgroundColor: '#4CAF50',
    position: 'absolute',
  },

  headerTextContainer: {
    marginLeft: wp(3),
    // backgroundColor:"pink",
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'space-between',
  },
  messageListContainer: {
    flex: 1,
  },
  scrollViewContent: {
    flexGrow: 1,
    paddingVertical: hp(2),
  },
  dateHeaderContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    marginVertical: hp(1),
  },
  dateHeader: {
    backgroundColor: colors.lightGrey6,
    paddingHorizontal: wp(3),
    paddingVertical: hp(0.5),
    borderRadius: wp(2),
  },
  dateHeaderText: {
    fontSize: wp(3.5),
    color: colors.grey,
  },
  inputContainer: {
    flexDirection: 'row',
    padding: Platform.OS === 'ios' ? wp(4) : wp(3),
    alignItems: 'flex-end',
    borderTopWidth: 1,
  },
  inputWrapper: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'flex-end',
    backgroundColor: colors.lightGrey6,
    borderRadius: wp(9),
    paddingRight: wp(4), // Space for attachment button
    paddingLeft: wp(2), // Space for text input
  },
  textInput: {
    flex: 1,
    color: 'black',
    paddingVertical: hp(1.5),
    minHeight: hp(6),
    maxHeight: MAX_HEIGHT,
    paddingHorizontal: wp(4),
    backgroundColor: 'transparent', // Make background transparent as wrapper has it
    marginRight: 0, // Remove margin as we're handling spacing in wrapper
  },
  attachmentButton: {
    paddingBottom: hp(1.5), // Adjust to match text input padding
    paddingHorizontal: wp(2),
  },
  sendButton: {
    padding: wp(2.4),
    borderRadius: wp(6),
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: wp(2), // Space between input wrapper and send button
    bottom: wp(2),
  },
  // inlineSendButton: {
  //   backgroundColor: colors.primary, // or your themeColor
  //   borderRadius: wp(6),
  //   padding: wp(2),
  //   marginRight: wp(1),
  //   alignSelf: 'flex-end',
  // },

  // inputContainer: {
  //   flexDirection: 'row',
  //   padding: Platform.OS === 'ios' ? wp(4) : wp(3),
  //   // backgroundColor: 'white',
  //   alignItems: 'flex-end',
  //   borderTopWidth: 1,
  //   // borderTopColor: 'rgba(0, 0, 0, 0.1)',

  // },
  // textInput: {
  //   flex: 1,
  //   color: 'black',
  //   paddingVertical: hp(1.5),
  //   minHeight: hp(6),
  //   maxHeight: MAX_HEIGHT,
  //   borderRadius: wp(2),
  //   paddingHorizontal: wp(4),
  //   backgroundColor: colors.lightGrey6,
  //   marginRight: wp(2),
  // },
  // sendButton: {
  //   // paddingBottom: Platform.OS === 'ios' ? wp(2) : 0,
  //   padding:wp(2.8),
  //   borderRadius:wp(6),
  //   alignItems:"center",
  //   justifyContent:"center",
  //   bottom:wp(1.5)
  // },
  nameContainer: {
    flexDirection: 'column',
    // alignItems: 'center',
    // gap: wp(9),
    // backgroundColor:"pink"
  },
  emptyStateContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: wp(4),
  },
  emptyStateText: {
    marginTop: hp(2),
    textAlign: 'center',
    opacity: 0.7,
  },
  selectedFileContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: colors.lightGrey6,
    padding: wp(2),
    borderRadius: wp(1),
    marginBottom: wp(0.5),
    marginHorizontal: wp(2),
  },
  removeFileButton: {
    marginLeft: wp(2),
    padding: wp(1),
  },
});

export default ChatScreen;
